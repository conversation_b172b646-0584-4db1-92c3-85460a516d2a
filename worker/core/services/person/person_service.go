package person

import (
	"context"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/event"
	"digisac-go/worker/core/utils/contact_utils"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"
)

type PersonService struct {
	personRepository  repositories.PersonRepository
	contactRepository repositories.ContactRepository
	serviceRepository repositories.ServiceRepository
	eventService      *event.EventService
}

func NewPersonService(
	personRepository repositories.PersonRepository,
	contactRepository repositories.ContactRepository,
	serviceRepository repositories.ServiceRepository,
	eventService *event.EventService,
) *PersonService {
	return &PersonService{
		personRepository:  personRepository,
		contactRepository: contactRepository,
		serviceRepository: serviceRepository,
		eventService:      eventService,
	}
}

func (s *PersonService) LinkContactToPerson(ctx context.Context, contact *models.Contact, tx *gormrepository.Tx, etx *event.Etx) error {
	if contact.Service == nil {
		return fmt.Errorf("contact service is nil")
	}

	if contact.PersonId != uuid.Nil {
		return nil
	}

	idFromServiceVariants := contact_utils.GenerateIdFromServiceVariants(contact.IdFromService, contact.Service.Type)

	// Busca outro contato com a mesma variante do idFromService que tenha personId
	contactWithPerson, err := s.contactRepository.FindOne(
		ctx,
		repositories.WithTx(tx),
		repositories.WithQueryStruct(map[string]interface{}{
			"accountId":  contact.AccountId,
			"archivedAt": nil,
		}),
		repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
			return d.Where(`"idFromService" IN ?`, idFromServiceVariants).Where(`"id" != ?`, contact.Id).Where(`"personId" IS NOT NULL`)
		}),
	)

	if err != nil && err.Error() != "record not found" {
		return fmt.Errorf("failed to find contact with person: %w", err)
	}

	if contactWithPerson != nil {
		contact.PersonId = contactWithPerson.PersonId
		err = s.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))

		if err != nil {
			return fmt.Errorf("failed to update contact: %w", err)
		}

		err = s.eventService.Dispatch(ctx, event.EventContactUpdated, contact, &event.EventServiceDispatchOptions{
			DebounceKey: contact.Id.String(),
			Etx:         etx,
		})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
			return fmt.Errorf("failed to dispatch contact updated event: %w", err)
		}

		return nil
	}

	return nil
}
