package event

import (
	"context"
	"digisac-go/worker/core/services/dispatcher/queue"
	"log/slog"
	"time"
)

type EventTypeEnum string

const (
	EventMessageCreated EventTypeEnum = "message.created"
	EventMessageUpdated EventTypeEnum = "message.updated"

	EventContactCreated EventTypeEnum = "contact.created"
	EventContactUpdated EventTypeEnum = "contact.updated"

	EventServiceCreated EventTypeEnum = "service.created"
	EventServiceUpdated EventTypeEnum = "service.updated"

	EventTicketCreated     EventTypeEnum = "ticket.created"
	EventTicketUpdated     EventTypeEnum = "ticket.updated"
	EventTicketOpened      EventTypeEnum = "ticket.opened"
	EventTicketClosed      EventTypeEnum = "ticket.closed"
	EventTicketTransfer    EventTypeEnum = "ticket.transfer"
	EventTicketInactive    EventTypeEnum = "ticket.inactive"
	EventTicketBeforeClose EventTypeEnum = "ticket.before_close"

	EventUserTransfer EventTypeEnum = "user.transfer"
)

type EventServiceDispatchOptions struct {
	Debounce<PERSON>ey   string
	DebounceDelay time.Duration
	Etx           *Etx
}

type EventService struct {
	queueJobsDispatcherService *queue.QueueJobsDispatcherService
	debouncedEvents            map[string]time.Time
	// debouncedEventsMu          sync.Mutex
	defaultDebounceDelay time.Duration
}

type Event struct {
	ctx       context.Context
	eventName EventTypeEnum
	payload   interface{}
	options   *EventServiceDispatchOptions
}

type Etx struct {
	eventService *EventService
	events       []*Event
}

func NewEtx(eventService *EventService) *Etx {
	return &Etx{
		eventService: eventService,
		events:       make([]*Event, 0),
	}
}

func NewEventService(queueJobsDispatcherService *queue.QueueJobsDispatcherService) *EventService {
	return &EventService{
		queueJobsDispatcherService: queueJobsDispatcherService,
		debouncedEvents:            make(map[string]time.Time),
		defaultDebounceDelay:       time.Second * 1,
	}
}

// @TODO: Para maior desempenho rodar de forma assincrona com buffer, consumir com um um workers de 10 goroutines para não alocar muita ram
// Usar o debounce
func (e *EventService) Dispatch(ctx context.Context, eventName EventTypeEnum, payload interface{}, options *EventServiceDispatchOptions) error {
	// if options != nil && options.Etx != nil {
	// 	options.Etx.events = append(options.Etx.events, &Event{
	// 		ctx:       ctx,
	// 		eventName: eventName,
	// 		payload:   payload,
	// 		options:   options,
	// 	})
	// 	return nil
	// }

	// if options != nil && options.DebounceKey != "" {
	// 	newOptions := *options
	// 	go func() { _ = e.dispatchDebounced(ctx, eventName, payload, &newOptions) }()
	// 	return nil
	// }

	return e.dispatchEvent(ctx, eventName, payload)
}

func (e *EventService) Transaction(ctx context.Context, fn func(ctx context.Context, etx *Etx) error) (err error) {
	etx := NewEtx(e)

	if err := fn(ctx, etx); err != nil {
		return err
	}

	for _, event := range etx.events {
		var eventOptions *EventServiceDispatchOptions

		if event.options != nil {
			eventOptions = event.options
			eventOptions.Etx = nil
		}

		if err := e.Dispatch(ctx, event.eventName, event.payload, eventOptions); err != nil {
			return err
		}
	}
	return
}

func (e *EventService) BeginTransaction(nestedEtx ...*Etx) *Etx {
	if nestedEtx != nil {
		return nestedEtx[0]
	}

	return NewEtx(e)
}

func (e *Etx) Commit() (err error) {
	for _, event := range e.events {
		var eventOptions *EventServiceDispatchOptions

		if event.options != nil {
			eventOptions = event.options
			eventOptions.Etx = nil
		}

		if err := e.eventService.Dispatch(event.ctx, event.eventName, event.payload, eventOptions); err != nil {
			return err
		}
	}

	e.events = nil

	return nil
}

// func (e *EventService) dispatchDebounced(ctx context.Context, eventName EventTypeEnum, payload interface{}, options *EventServiceDispatchOptions) error {
// 	if options != nil && options.DebounceKey != "" {
// 		eventKey := string(eventName) + "_" + options.DebounceKey
// 		now := time.Now()

// 		if options.DebounceDelay == 0 {
// 			options.DebounceDelay = e.defaultDebounceDelay
// 		}

// 		e.debouncedEventsMu.Lock()
// 		e.debouncedEvents[eventKey] = now
// 		e.debouncedEventsMu.Unlock()

// 		time.Sleep(options.DebounceDelay)

// 		e.debouncedEventsMu.Lock()
// 		lastEventTime, exists := e.debouncedEvents[eventKey]
// 		if exists && now.Before(lastEventTime) {
// 			e.debouncedEventsMu.Unlock()
// 			slog.DebugContext(ctx, "Event discarded due to debounce",
// 				slog.String("eventName", string(eventName)),
// 				slog.String("eventKey", eventKey),
// 			)
// 			return nil
// 		}
// 		delete(e.debouncedEvents, options.DebounceKey)
// 		e.debouncedEventsMu.Unlock()
// 	}

// 	return e.dispatchEvent(ctx, eventName, payload)
// }

func (e *EventService) dispatchEvent(ctx context.Context, eventName EventTypeEnum, payload interface{}) error {
	slog.DebugContext(ctx, "Dispatching event",
		slog.String("eventName", string(eventName)),
	)

	event := string(eventName)

	err := e.queueJobsDispatcherService.Dispatch(
		ctx,
		event,
		map[string]interface{}{
			"event": event,
			"data":  payload,
		},
		&queue.QueueJobsDispatcherDispatchOptions{
			HashKey: event,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch event",
			slog.String("eventName", event),
			slog.Any("payload", payload),
		)
		return err
	}

	return nil
}
