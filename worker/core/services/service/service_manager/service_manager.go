package service_manager

import (
	"context"
	"errors"
	"fmt"
	"log/slog"

	"digisac-go/worker/config"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/event"
)

type ServiceManagerService struct {
	adapterManager    *service_manager.ServiceManager
	serviceRepository repositories.ServiceRepository
	config            *config.Config
	eventService      *event.EventService
}

func NewServiceManagerService(
	adapterManager *service_manager.ServiceManager,
	serviceRepository repositories.ServiceRepository,
	config *config.Config,
	eventService *event.EventService,
) *ServiceManagerService {
	return &ServiceManagerService{
		adapterManager:    adapterManager,
		serviceRepository: serviceRepository,
		config:            config,
		eventService:      eventService,
	}
}

func (m *ServiceManagerService) getWebhookUrlFromService(ctx context.Context, service *models.Service) (url string, err error) {
	path := ""

	switch service.Type {
	case "telegram":
		path = "/telegram/webhook"
	case "whatsapp":
		path = "/whatsapp-server-pod-webhook"
	case "whatsapp-business":
		path = "/whatsapp-business-webhook"
	default:
		slog.ErrorContext(ctx, "unknown service type for webhook URL creation", "serviceType", service.Type)
		return "", errors.New("unknown service type for webhook URL creation")
	}

	url = m.config.ApiPublicUrl + path + "/" + service.Id.String()

	return url, nil
}

func (m *ServiceManagerService) Start(ctx context.Context, startData *payloads.StartServicePayload) (response bool, err error) {
	adapter, err := m.adapterManager.GetAdapter(ctx, startData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Start get adapter failed", slog.String("error", err.Error()), slog.Any("startData", startData))
		return false, fmt.Errorf("Start get adapter failed: %w", err)
	}

	service, err := m.serviceRepository.FindById(ctx, startData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Start find service failed", slog.String("error", err.Error()), slog.Any("startData", startData))
		return false, fmt.Errorf("Start find service failed: %w", err)
	}

	err = m.serviceRepository.UpdateByIdInPlace(ctx, service.Id, service, func() {
		service.Data.Status.IsConnected = false
		service.Data.Status.IsStarted = false
		service.Data.Status.IsStarting = true
		service.Data.Error = ""
	})

	if err != nil {
		slog.ErrorContext(ctx, "Start update service (status) failed",
			slog.String("error", err.Error()),
			slog.Any("startData", startData),
			slog.Any("service", service))
		return false, fmt.Errorf("Start update service (status) failed: %w", err)
	}

	err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
		DebounceKey: service.Id.String(),
	})

	if err != nil {
		slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
		return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
	}

	err = adapter.Start(ctx, startData.ServiceId)

	service, err2 := m.serviceRepository.FindById(ctx, startData.ServiceId)

	if err2 != nil {
		slog.ErrorContext(ctx, "Start find service (after adapter.Start) failed", slog.String("error", err2.Error()), slog.Any("startData", startData))
		return false, fmt.Errorf("Start find service (after adapter.Start) failed: %w", err2)
	}

	if err != nil {
		service.Data.Status.IsStarting = false
		service.Data.Error = err.Error()

		err3 := m.serviceRepository.UpdateById(ctx, service.Id, service)

		if err3 != nil {
			slog.ErrorContext(ctx, "Start update service (error) failed",
				slog.String("error", err3.Error()),
				slog.Any("startData", startData),
				slog.Any("service", service))

			return false, fmt.Errorf("Start update service (error) failed: %w", err3)
		}

		err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
			DebounceKey: service.Id.String(),
		})

		if err != nil {
			slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
			return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
		}

		return false, fmt.Errorf("Start adapter.Start failed: %w", err)
	}

	url, err := m.getWebhookUrlFromService(ctx, service)

	if err != nil {
		service.Data.Status.IsStarting = false
		service.Data.Error = err.Error()

		err = m.serviceRepository.UpdateById(ctx, service.Id, service)

		if err != nil {
			slog.ErrorContext(ctx, "Start update service (webhook URL error) failed", slog.String("error", err.Error()), slog.Any("startData", startData), slog.Any("service", service))
			return false, fmt.Errorf("Start update service (webhook URL error) failed: %w", err)
		}

		err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
			DebounceKey: service.Id.String(),
		})

		if err != nil {
			slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
			return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
		}

		return false, fmt.Errorf("Start getWebhookUrlFromService failed: %w", err)
	}

	if !m.config.DisableWabaWebhookUrlSet {
		slog.InfoContext(ctx, "Setting up webhook", slog.String("serviceId", startData.ServiceId.String()), slog.String("url", url))
		err = adapter.SetWebhook(ctx, startData.ServiceId, url)
	}

	service, err2 = m.serviceRepository.FindById(ctx, startData.ServiceId)

	if err2 != nil {
		slog.ErrorContext(ctx, "Start find service (after adapter.SetWebhook) failed", slog.String("error", err2.Error()), slog.Any("startData", startData))
		return false, fmt.Errorf("Start find service (after adapter.SetWebhook) failed: %w", err2)
	}

	if err != nil {
		service.Data.Status.IsStarting = false
		service.Data.Error = err.Error()

		err3 := m.serviceRepository.UpdateById(ctx, service.Id, service)

		if err3 != nil {
			slog.ErrorContext(ctx, "Start update service (set webhook error) failed",
				slog.String("error", err3.Error()),
				slog.Any("startData", startData),
				slog.Any("service", service))
			return false, fmt.Errorf("Start update service (set webhook error) failed: %w", err3)
		}

		err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
			DebounceKey: service.Id.String(),
		})

		if err != nil {
			slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
			return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
		}

		return false, fmt.Errorf("Start adapter.SetWebhook failed: %w", err)
	}

	err = m.serviceRepository.UpdateByIdInPlace(ctx, service.Id, service, func() {
		service.Data.Status.IsStarting = false
		service.Data.Status.IsConnected = true
		service.Data.Status.IsStarted = true
		service.Data.Error = ""
	})

	if err != nil {
		slog.ErrorContext(ctx, "Start update service (final status) failed",
			slog.String("error", err.Error()),
			slog.Any("startData", startData),
			slog.Any("service", service))
		return false, fmt.Errorf("Start update service (final status) failed: %w", err)
	}

	err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
		DebounceKey: service.Id.String(),
	})

	if err != nil {
		slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
		return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
	}

	return true, nil
}

func (m *ServiceManagerService) Restart(ctx context.Context, restartData *payloads.RestartServicePayload) (response bool, err error) {
	_, err = m.Shutdown(ctx, &payloads.ShutdownServicePayload{
		ServiceId: restartData.ServiceId,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Restart shutdown failed", slog.String("error", err.Error()), slog.Any("restartData", restartData))
		return false, fmt.Errorf("Restart shutdown failed: %w", err)
	}

	_, err = m.Start(ctx, &payloads.StartServicePayload{
		ServiceId: restartData.ServiceId,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Restart start failed", slog.String("error", err.Error()), slog.Any("restartData", restartData))
		return false, fmt.Errorf("Restart start failed: %w", err)
	}

	return true, nil
}

func (m *ServiceManagerService) Shutdown(ctx context.Context, shutdownData *payloads.ShutdownServicePayload) (response bool, err error) {
	adapter, err := m.adapterManager.GetAdapter(ctx, shutdownData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Shutdown get adapter failed", slog.String("error", err.Error()), slog.Any("shutdownData", shutdownData))
		return false, fmt.Errorf("Shutdown get adapter failed: %w", err)
	}

	service, err := m.serviceRepository.FindById(ctx, shutdownData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Shutdown find service failed", slog.String("error", err.Error()), slog.Any("shutdownData", shutdownData))
		return false, fmt.Errorf("Shutdown find service failed: %w", err)
	}

	err = adapter.Shutdown(ctx, shutdownData.ServiceId)

	service.Data.Status = &models.ServiceDataStatus{
		IsStarting:  false,
		IsConnected: false,
		IsStarted:   false,
	}

	if err != nil {
		service.Data.Error = err.Error()

		err2 := m.serviceRepository.UpdateById(ctx, shutdownData.ServiceId, service)

		if err2 != nil {
			slog.ErrorContext(ctx, "Shutdown update service (error) failed",
				slog.String("error", err2.Error()),
				slog.Any("shutdownData", shutdownData),
				slog.Any("service", service))
			return false, fmt.Errorf("Shutdown update service (error) failed: %w", err2)
		}

		err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
			DebounceKey: service.Id.String(),
		})

		if err != nil {
			slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
			return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
		}

		return false, fmt.Errorf("Shutdown adapter.Shutdown failed: %w", err)
	}

	err = m.serviceRepository.UpdateById(ctx, shutdownData.ServiceId, service)

	if err != nil {
		slog.ErrorContext(ctx, "Shutdown update service (final status) failed",
			slog.String("error", err.Error()),
			slog.Any("shutdownData", shutdownData),
			slog.Any("service", service))
		return false, fmt.Errorf("Shutdown update service (final status) failed: %w", err)
	}

	err = m.eventService.Dispatch(ctx, event.EventServiceUpdated, service, &event.EventServiceDispatchOptions{
		DebounceKey: service.Id.String(),
	})

	if err != nil {
		slog.ErrorContext(ctx, "failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
		return false, fmt.Errorf("failed to dispatch service updated event: %w", err)
	}

	return true, nil
}

func (m *ServiceManagerService) Logout(ctx context.Context, logoutData *payloads.LogoutServicePayload) (response bool, err error) {
	adapter, err := m.adapterManager.GetAdapter(ctx, logoutData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Logout get adapter failed", slog.String("error", err.Error()), slog.Any("logoutData", logoutData))
		return false, fmt.Errorf("Logout get adapter failed: %w", err)
	}

	err = adapter.Logout(ctx, logoutData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Logout adapter.Logout failed", slog.String("error", err.Error()), slog.Any("logoutData", logoutData))
		return false, fmt.Errorf("Logout adapter.Logout failed: %w", err)
	}

	_, err = m.Shutdown(ctx, &payloads.ShutdownServicePayload{
		ServiceId: logoutData.ServiceId,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Logout shutdown failed", slog.String("error", err.Error()), slog.Any("logoutData", logoutData))
		return false, fmt.Errorf("Logout shutdown failed: %w", err)
	}

	return true, nil
}

func (m *ServiceManagerService) Takeover(ctx context.Context, takeoverData *payloads.TakeoverServicePayload) (response bool, err error) {
	adapter, err := m.adapterManager.GetAdapter(ctx, takeoverData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Takeover get adapter failed", slog.String("error", err.Error()), slog.Any("takeoverData", takeoverData))
		return false, fmt.Errorf("Takeover get adapter failed: %w", err)
	}

	err = adapter.Takeover(ctx, takeoverData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Takeover adapter.Takeover failed", slog.String("error", err.Error()), slog.Any("takeoverData", takeoverData))
		return false, fmt.Errorf("Takeover adapter.Takeover failed: %w", err)
	}

	return true, nil
}
