package message_sender

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"

	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/account"
	"digisac-go/worker/core/services/answer"
	"digisac-go/worker/core/services/crypto"
	httpDispatcherService "digisac-go/worker/core/services/dispatcher/http"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"
	"digisac-go/worker/core/services/event"
	"digisac-go/worker/core/services/message/message_link"
	"digisac-go/worker/core/services/sticker"
	"digisac-go/worker/core/services/storage"
	"digisac-go/worker/core/services/ticket"
	"digisac-go/worker/core/utils/common"
	"digisac-go/worker/core/utils/concurrency"
	lock "digisac-go/worker/core/utils/lock_utils"
)

type SendService struct {
	accountCryptor                    *crypto.AccountCryptor
	messageRepository                 repositories.MessageRepository
	templateRepository                repositories.WhatsappBusinessTemplateRepository
	contactRepository                 repositories.ContactRepository
	ticketRepository                  repositories.TicketRepository
	fileRepository                    repositories.FileRepository
	campaignMessageProgressRepository repositories.CampaignMessageProgressRepository
	ticketService                     *ticket.TicketService
	messageLinkService                *message_link.LinkService
	stickerService                    *sticker.StickerService
	adapterManager                    *service_manager.ServiceManager
	queueDispatcher                   *queueDispatcher.QueueJobsDispatcherService
	httpJobsDispatcherService         *httpDispatcherService.HttpJobsDispatcherService
	storageService                    *storage.StorageService
	accountService                    *account.AccountUsedHsmService
	distributedLockFactory            concurrency.DistributedLockFactory
	answerService                     *answer.AnswerService
	Config                            *config.Config
	eventService                      *event.EventService
}

func NewSendMessageService(
	accountCryptor *crypto.AccountCryptor,
	messageRepository repositories.MessageRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	contactRepository repositories.ContactRepository,
	ticketRepository repositories.TicketRepository,
	fileRepository repositories.FileRepository,
	campaignMessageProgressRepository repositories.CampaignMessageProgressRepository,
	ticketService *ticket.TicketService,
	messageLinkService *message_link.LinkService,
	stickerService *sticker.StickerService,
	adapterManager *service_manager.ServiceManager,
	queueDispatcher *queueDispatcher.QueueJobsDispatcherService,
	httpJobsDispatcherService *httpDispatcherService.HttpJobsDispatcherService,
	storageService *storage.StorageService,
	accountService *account.AccountUsedHsmService,
	distributedLockFactory concurrency.DistributedLockFactory,
	answerService *answer.AnswerService,
	config *config.Config,
	eventService *event.EventService,
) *SendService {
	return &SendService{
		accountCryptor:                    accountCryptor,
		messageRepository:                 messageRepository,
		templateRepository:                templateRepository,
		contactRepository:                 contactRepository,
		ticketRepository:                  ticketRepository,
		fileRepository:                    fileRepository,
		campaignMessageProgressRepository: campaignMessageProgressRepository,
		ticketService:                     ticketService,
		messageLinkService:                messageLinkService,
		stickerService:                    stickerService,
		adapterManager:                    adapterManager,
		queueDispatcher:                   queueDispatcher,
		httpJobsDispatcherService:         httpJobsDispatcherService,
		storageService:                    storageService,
		accountService:                    accountService,
		distributedLockFactory:            distributedLockFactory,
		answerService:                     answerService,
		Config:                            config,
		eventService:                      eventService,
	}
}

func (m *SendService) GetMessageType(file *models.File) (messageType string) {
	if file == nil {
		return "chat"
	}

	switch strings.Split(file.Mimetype, "/")[0] {
	case "audio":
		messageType = "audio"
	case "image":
		messageType = "image"
	case "video":
		messageType = "video"
	default:
		messageType = "document"
	}

	return messageType
}

func (m *SendService) GetContactByIdOrNumber(ctx context.Context, createData *payloads.SendMessagePayload, tx *gormrepository.Tx) (contact *models.Contact, err error) {
	if tx == nil {
		tx = m.messageRepository.BeginTransaction()
		defer tx.Finish(&err)
	}

	where := map[string]interface{}{
		"accountId":  createData.AccountId,
		"archivedAt": nil,
	}

	if createData.ServiceId != uuid.Nil {
		where["serviceId"] = createData.ServiceId
	}

	if createData.ContactId != uuid.Nil {
		where["id"] = createData.ContactId
	} else if createData.Number != "" {
		// packages/back/src/core/utils/whatsapp/comparableIdFromService.ts
		where["idFromService"] = createData.Number //alterar <NAME_EMAIL>, usar comparableIdFromService
	}

	contact, err = m.contactRepository.FindOne(ctx,
		repositories.WithTx(tx),
		repositories.WithQueryStruct(where),
		repositories.WithRelations("Service", "Account", "CurrentTicket", "CurrentTicket.CurrentTicketTransfer"),
	)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find contact", slog.String("error", err.Error()), slog.Any("createData", createData))
		return nil, fmt.Errorf("failed to find contact: %w", err)
	}

	return contact, nil
}

func (m *SendService) RevokeReaction(ctx context.Context, revokeData *payloads.RevokeReactionPayload) (response bool, err error) {
	message, err := m.messageRepository.FindById(ctx, revokeData.MessageId)

	if err != nil {
		slog.ErrorContext(ctx, "RevokeReaction find message failed", slog.String("error", err.Error()), slog.Any("revokeData", revokeData))
		return false, fmt.Errorf("RevokeReaction find message failed: %w", err)
	}

	adapter, err := m.adapterManager.GetAdapter(ctx, message.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "RevokeReaction get adapter failed", slog.String("error", err.Error()), slog.Any("revokeData", revokeData))
		return false, fmt.Errorf("RevokeReaction get adapter failed: %w", err)
	}

	messageReaction, err := m.messageRepository.FindById(ctx, message.ReactionParentMessageId, repositories.WithRelations("Contact"))

	if err != nil {
		slog.ErrorContext(ctx, "RevokeReaction find parent message reaction failed", slog.String("error", err.Error()), slog.Any("revokeData", revokeData), slog.String("message.ReactionParentMessageId", message.ReactionParentMessageId.String()))
		return false, fmt.Errorf("RevokeReaction find parent message reaction failed: %w", err)
	}

	response, err = adapter.RevokeReaction(ctx, message.ServiceId, &adapter_types.RevokeReactionPayload{
		To:        messageReaction.Contact.IdFromService,
		MessageId: messageReaction.IdFromService,
	})

	if err != nil {
		slog.ErrorContext(ctx, "RevokeReaction adapter revoke failed", slog.String("error", err.Error()), slog.Any("revokeData", revokeData), slog.String("message.ServiceId", message.ServiceId.String()), slog.String("messageReaction.Contact.IdFromService", messageReaction.Contact.IdFromService), slog.String("messageReaction.IdFromService", messageReaction.IdFromService))
		return false, fmt.Errorf("RevokeReaction adapter revoke failed: %w", err)
	}

	err = m.messageRepository.UpdateByIdInPlace(ctx, message.Id, message, func() {
		message.Text = ""
		message.Data.Ack = 1
	})

	if err != nil {
		slog.ErrorContext(ctx, "RevokeReaction update message failed", slog.String("error", err.Error()), slog.Any("revokeData", revokeData), slog.Any("message", message))
		return false, fmt.Errorf("RevokeReaction update message failed: %w", err)
	}

	err = m.eventService.Dispatch(ctx, event.EventMessageUpdated, message, &event.EventServiceDispatchOptions{
		DebounceKey: message.Id.String(),
	})

	if err != nil {
		slog.ErrorContext(ctx, "RevokeReaction failed on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
		return false, fmt.Errorf("RevokeReaction failed on dispatch message updated event: %w", err)
	}

	return response, nil
}
func (m *SendService) SendReaction(ctx context.Context, reactionData *payloads.SendReactionPayload) (response bool, err error) {
	response = false

	message, err := m.messageRepository.FindById(ctx, reactionData.MessageId, repositories.WithRelations("Contact", "Account"))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find message by Id", slog.String("MessageId", reactionData.MessageId.String()), slog.String("error", err.Error()))
		return response, fmt.Errorf("failed to find message by Id: %w", err)
	}

	if message.IdFromService == "" {
		slog.ErrorContext(ctx, "Invalid message", slog.String("MessageId", reactionData.MessageId.String()))
		return response, errors.New("invalid message")
	}

	if message.Contact == nil {
		slog.ErrorContext(ctx, "Contact not found for message", slog.String("MessageId", reactionData.MessageId.String()))
		return response, errors.New("contact not found")
	}

	adapter, err := m.adapterManager.GetAdapter(ctx, message.ServiceId)
	if err != nil {
		slog.ErrorContext(ctx, "SendReaction failed to get adapter", slog.String("ServiceId", message.ServiceId.String()), slog.String("error", err.Error()), slog.Any("reactionData", reactionData))
		return response, fmt.Errorf("SendReaction failed to get adapter: %w", err)
	}

	queueKey, err := lock.GetContactLockKey(message.AccountId, message.ServiceId, message.ContactId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get contact lock key", slog.String("AccountId", message.AccountId.String()), slog.String("ServiceId", message.ServiceId.String()), slog.String("ContactId", message.ContactId.String()), slog.String("error", err.Error()))
		return false, fmt.Errorf("failed to get contact lock key: %w", err)
	}

	queue := m.distributedLockFactory(queueKey, 2*time.Minute)

	_, err = queue.Run(ctx, func() (interface{}, error) {
		etx := m.eventService.BeginTransaction()

		sendReactionBody := &adapter_types.SendReactionPayload{
			To:        message.Contact.IdFromService,
			MessageId: message.IdFromService,
			Reaction:  reactionData.ReactionEmojiRendered,
		}

		response, err := adapter.SendReaction(ctx, message.ServiceId, sendReactionBody)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to send reaction via adapter", slog.String("ServiceId", message.ServiceId.String()), slog.Any("sendReactionBody", sendReactionBody), slog.String("error", err.Error()))
			return response, fmt.Errorf("failed to send reaction via adapter: %w", err)
		}

		tx := m.messageRepository.BeginTransaction()
		defer tx.Finish(&err)

		transaction := repositories.WithTx(tx)
		messageReaction, err := m.messageRepository.FindOne(ctx, transaction, repositories.WithQueryStruct(map[string]interface{}{
			"reactionParentMessageId": message.Id,
			"type":                    "reaction",
			"contactId":               message.Contact.Id,
			"isFromMe":                true,
			"fromId":                  message.Contact.Id,
			"serviceId":               message.ServiceId,
			"accountId":               message.AccountId,
		}))
		if err != nil && err.Error() != "record not found" {
			slog.ErrorContext(ctx, "Failed to find existing reaction message", slog.String("reactionParentMessageId", message.Id.String()), slog.String("type", "reaction"), slog.String("contactId", message.Contact.Id.String()), slog.String("fromId", message.Contact.Id.String()), slog.String("serviceId", message.ServiceId.String()), slog.String("accountId", message.AccountId.String()), slog.String("error", err.Error()))
			return false, fmt.Errorf("failed to find existing reaction message: %w", err)
		}

		text, err := m.accountCryptor.EncryptTextForAccount(ctx, message.Account, reactionData.ReactionEmojiRendered, false)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to encrypt reaction text for account", slog.String("AccountId", message.AccountId.String()), slog.String("ReactionEmojiRendered", reactionData.ReactionEmojiRendered), slog.String("error", err.Error()))
			return false, fmt.Errorf("failed to encrypt reaction text for account: %w", err)
		}

		timestamp := time.Now()

		var userId uuid.UUID
		if message.Contact.CurrentTicketId != uuid.Nil {
			currentTicket, err := m.ticketRepository.FindById(ctx, message.Contact.CurrentTicketId, transaction)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to find current ticket for contact", slog.String("CurrentTicketId", message.Contact.CurrentTicketId.String()), slog.String("error", err.Error()))
				return false, fmt.Errorf("failed to find current ticket for contact: %w", err)
			}
			userId = currentTicket.UserId
		}

		var lastReactionMessage *models.Message

		// Só pode existir uma reação por mensagem, caso já exista apenas atualiza
		if messageReaction != nil {
			messageReaction.Text = text
			messageReaction.Timestamp = &timestamp
			messageReaction.UserId = userId

			err = m.messageRepository.UpdateById(ctx, messageReaction.Id, messageReaction, transaction)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to update existing reaction message", slog.String("MessageId", messageReaction.Id.String()), slog.String("error", err.Error()))
				return false, fmt.Errorf("failed to update existing reaction message: %w", err)
			}

			err = m.eventService.Dispatch(ctx, event.EventMessageUpdated, messageReaction, &event.EventServiceDispatchOptions{
				DebounceKey: messageReaction.Id.String(),
				Etx:         etx,
			})

			if err != nil {
				slog.ErrorContext(ctx, "RevokeReaction failed on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", messageReaction))
				return false, fmt.Errorf("RevokeReaction failed on dispatch message updated event: %w", err)
			}

			lastReactionMessage = messageReaction

		} else {
			createReactionMessage := &models.Message{
				Text:          text,
				IdFromService: message.Id.String() + "-" + fmt.Sprintf("%v", timestamp.Unix()),
				Timestamp:     &timestamp,
				Type:          "reaction",
				Data: &models.MessageData{
					Ack:     1,
					IsNew:   false,
					IsFirst: false,
				},
				IsFromMe:                true,
				FromId:                  message.ContactId,
				ContactId:               message.ContactId,
				ServiceId:               message.ServiceId,
				AccountId:               message.AccountId,
				Origin:                  "user",
				UserId:                  userId,
				ReactionParentMessageId: message.Id,
			}

			err = m.messageRepository.Create(ctx, createReactionMessage, transaction)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to create new reaction message", slog.String("MessageId", createReactionMessage.Id.String()), slog.String("error", err.Error()))
				return false, fmt.Errorf("failed to create new reaction message: %w", err)
			}

			err = m.eventService.Dispatch(ctx, event.EventMessageCreated, createReactionMessage, &event.EventServiceDispatchOptions{
				DebounceKey: createReactionMessage.Id.String(),
				Etx:         etx,
			})

			if err != nil {
				slog.ErrorContext(ctx, "RevokeReaction failed on dispatch message created event", slog.String("error", err.Error()), slog.Any("message", createReactionMessage))
				return false, fmt.Errorf("RevokeReaction failed on dispatch message created event: %w", err)
			}

			lastReactionMessage = createReactionMessage
		}

		if !message.Data.HasReaction {
			message.Data.HasReaction = true

			err = m.messageRepository.UpdateById(ctx, message.Id, message, transaction)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to update message data", slog.String("MessageId", message.Id.String()), slog.String("error", err.Error()))
				return false, fmt.Errorf("failed to update message data: %w", err)
			}

			err = m.eventService.Dispatch(ctx, event.EventMessageUpdated, message, &event.EventServiceDispatchOptions{
				DebounceKey: message.Id.String(),
				Etx:         etx,
			})

			if err != nil {
				slog.ErrorContext(ctx, "RevokeReaction failed on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
				return false, fmt.Errorf("RevokeReaction failed on dispatch message updated event: %w", err)
			}
		}

		if lastReactionMessage != nil && (message.Contact.LastMessageAt != nil || lastReactionMessage.Timestamp != nil && lastReactionMessage.Timestamp.After(*message.Contact.LastMessageAt)) {
			message.Contact.LastMessageId = lastReactionMessage.Id
			message.Contact.LastMessageAt = lastReactionMessage.Timestamp

			err = m.contactRepository.UpdateById(ctx, message.Contact.Id, message.Contact, transaction)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to update contact", slog.String("ContactId", message.Contact.Id.String()), slog.String("error", err.Error()))
				return false, fmt.Errorf("failed to update contact: %w", err)
			}

			err = m.eventService.Dispatch(ctx, event.EventContactUpdated, message.Contact, &event.EventServiceDispatchOptions{
				DebounceKey: message.Contact.Id.String(),
				Etx:         etx,
			})

			if err != nil {
				slog.ErrorContext(ctx, "RevokeReaction failed on dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", message.Contact))
				return false, fmt.Errorf("RevokeReaction failed on dispatch contact updated event: %w", err)
			}
		}

		err = etx.Commit()

		if err != nil {
			slog.ErrorContext(ctx, "RevokeReaction failed to commit event transaction", slog.String("error", err.Error()))
			return false, fmt.Errorf("RevokeReaction failed to commit event transaction: %w", err)
		}

		return response, err
	})

	return response, err
}

func (m *SendService) VerifyBlockMessageRule(ctx context.Context, sendData *payloads.SendMessagePayload) (bool, error) {
	if sendData.IsComment || sendData.Type == "comment" || sendData.Type == "ticket" || sendData.Type == "unblock_message_rule" || sendData.Type == "summary" {
		return false, nil
	}

	response, err := m.httpJobsDispatcherService.Dispatch(ctx, "contact-blocked-by-message-rule-job", map[string]interface{}{
		"payload": map[string]interface{}{
			"contactId": sendData.ContactId,
		},
	}, httpDispatcherService.HttpJobsDispatcherDispatchOptions{
		UseWorkersNodeJs: true,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch contact-blocked-by-message-rule-job", slog.String("error", err.Error()), slog.String("contactId", sendData.ContactId.String()))
		return false, err
	}

	return response["isBlockedToSendMessage"].(bool), nil
}

func (m *SendService) SendMessage(ctx context.Context, sendData *payloads.SendMessagePayload) (message *models.Message, err error) {
	var contact *models.Contact
	var file *models.File

	contact, err = m.GetContactByIdOrNumber(ctx, sendData, nil)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get contact by Id or number", slog.Any("sendData", sendData), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get contact by Id or number: %w", err)
	}

	sendData.ContactId = contact.Id
	blocked, err := m.VerifyBlockMessageRule(ctx, sendData)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to verify block message rule", slog.String("error", err.Error()), slog.String("contactId", contact.Id.String()))
		return nil, fmt.Errorf("failed to verify block message rule: %w", err)
	}

	if blocked {
		return nil, fmt.Errorf("contact blocked to send message by rule")
	}

	queueKey, err := lock.GetContactLockKey(contact.AccountId, contact.ServiceId, contact.Id)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get contact lock key", slog.String("AccountId", contact.AccountId.String()), slog.String("ServiceId", contact.ServiceId.String()), slog.String("ContactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get contact lock key: %w", err)
	}

	queue := m.distributedLockFactory(queueKey, 2*time.Minute)

	_, err = queue.Run(ctx, func() (interface{}, error) {
		etx := m.eventService.BeginTransaction()
		tx := m.messageRepository.BeginTransaction()
		defer tx.Finish(&err)

		if !contact.Service.Data.Status.IsConnected {
			slog.ErrorContext(ctx, "Service disconnected", slog.String("ServiceId", contact.ServiceId.String()))
			return nil, errors.New("service disconnected")
		}

		if contact.Service.ArchivedAt != nil && !contact.Service.ArchivedAt.IsZero() {
			slog.ErrorContext(ctx, "Service archived", slog.String("ServiceId", contact.ServiceId.String()))
			return nil, errors.New("service archived")
		}

		if !sendData.IsFromSurvey &&
			contact.CurrentTicket != nil &&
			contact.CurrentTicket.Id != uuid.Nil &&
			sendData.ItemIndex == 0 &&
			!sendData.DontOpenTicket &&
			contact.CurrentTicket.UserId != sendData.UserId &&
			sendData.Origin != "bot" &&
			!contact.Block &&
			!sendData.IsComment &&
			!sendData.OutsideRequest {

			transferUserId := sendData.UserId
			if sendData.Origin == "campaign" && sendData.UserId == uuid.Nil {
				transferUserId = contact.CurrentTicket.UserId
			}

			transferDepartmentId := contact.CurrentTicket.DepartmentId
			if sendData.Origin == "campaign" && sendData.DepartmentId != uuid.Nil {
				transferDepartmentId = sendData.DepartmentId
			}

			data := &ticket.TransferData{
				Contact:      contact,
				UserId:       transferUserId,
				DepartmentId: transferDepartmentId,
				ByUserId:     sendData.UserId,
			}

			_, err = m.ticketService.TicketTransferService.TransferTicket(ctx, data, tx, etx)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to transfer ticket", slog.Any("data", data), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to transfer ticket: %w", err)
			}
		}

		if sendData.FileId != uuid.Nil {
			file, err = m.fileRepository.FindById(ctx, sendData.FileId, repositories.WithTx(tx))
		}

		if err != nil {
			slog.ErrorContext(ctx, "Failed to find file by Id", slog.String("FileId", sendData.FileId.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find file by Id: %w", err)
		}

		origin := "user"
		isFirst := false
		messageType := sendData.Type

		if file != nil {
			messageType = m.GetMessageType(file)
		} else if sendData.Type == "schedule" {
			messageType = "chat"
		}

		if sendData.StickerId != uuid.Nil {
			messageType = "sticker"
		}

		if sendData.HsmId != uuid.Nil || sendData.Hsm != nil && sendData.Hsm.Id != uuid.Nil {
			if sendData.HsmId == uuid.Nil && sendData.Hsm != nil && sendData.Hsm.Id != uuid.Nil {
				// Retrocompatibilidade
				sendData.HsmId = sendData.Hsm.Id
			}
			messageType = "hsm"
		}

		if messageType == "" {
			// Retrocompatibilidade
			messageType = "chat"
		}

		var interactive *models.InteractiveMessage

		if sendData.InteractiveMessage != nil {
			interactive = sendData.InteractiveMessage.Interactive
			messageType = "interactive"
		}

		var ticketUserId, ticketDepartmentId, userId uuid.UUID
		var text string
		var campaignData *ticket.CampaignData

		if sendData.Origin != "" {
			origin = sendData.Origin
		}

		if contact.LastMessageId == uuid.Nil {
			isFirst = true
		}

		if contact.CurrentTicket != nil {
			ticketUserId = contact.CurrentTicket.UserId
		}

		if contact.LastMessageId == uuid.Nil {
			ticketDepartmentId = contact.CurrentTicket.DepartmentId
		}

		if sendData.Type == "schedule" {
			userId = uuid.Nil
		} else if slices.Contains([]string{"user", "campaign"}, origin) {
			if sendData.UserId == uuid.Nil {
				userId = contact.CurrentTicket.UserId
			} else {
				userId = sendData.UserId
			}
		}

		if sendData.Text != "" {
			if messageType == "hsm" || messageType == "interactive" {
				text = ""
			} else {
				text, err = m.accountCryptor.EncryptTextForAccount(ctx, contact.Account, sendData.Text, false)
				if err != nil {
					slog.ErrorContext(ctx, "Failed to encrypt text for account", slog.String("AccountId", contact.AccountId.String()), slog.String("Text", sendData.Text), slog.String("error", err.Error()))
					return nil, fmt.Errorf("failed to encrypt text for account: %w", err)
				}
			}

		}

		now := time.Now()

		if sendData.IsComment || messageType == "comment" {
			message = &models.Message{
				Text:      text,
				Type:      messageType,
				IsComment: true,
				IsFromMe:  true,
				Timestamp: &now,
				ContactId: contact.Id,
				ServiceId: contact.ServiceId,
				AccountId: contact.AccountId,
				UserId:    userId,
			}

			err = m.messageRepository.Create(ctx, message, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "Failed to create comment message", slog.Any("message", message), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to create comment message: %w", err)
			}

			err = m.eventService.Dispatch(ctx, event.EventMessageCreated, message, &event.EventServiceDispatchOptions{
				Etx: etx,
			})

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage dispatch message created event failed", slog.String("error", err.Error()), slog.Any("message", message))
				return nil, fmt.Errorf("SendMessage dispatch message created event failed: %w", err)
			}

			if sendData.FileId != uuid.Nil {
				file := &models.File{
					AttachedId: message.Id,
				}
				err = m.fileRepository.UpdateById(ctx, sendData.FileId, file, repositories.WithTx(tx))
				if err != nil {
					slog.ErrorContext(ctx, "Failed to update file by Id", slog.String("FileId", sendData.FileId.String()), slog.String("error", err.Error()))
					return nil, fmt.Errorf("failed to update file by Id: %w", err)
				}
			}

			err := etx.Commit()

			if err != nil {
				slog.ErrorContext(ctx, "Failed to commit event transaction", slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to commit event transaction: %w", err)
			}

			return false, nil
		}

		// Interpolação de template
		if len(sendData.Parameters) > 0 {
			for i := range sendData.Parameters {
				for j := range sendData.Parameters[i].Parameters {
					if sendData.Parameters[i].Parameters[j].Type == "text" {
						sendData.Parameters[i].Parameters[j].Text = common.Interpolate(
							sendData.Parameters[i].Parameters[j].Text,
							map[string]any{
								"contact_name": contact.Name,
								// @TODO Permitir interpolar mais parametros? Atualmente só é permitido o contact_name
							},
						)
					}
				}
			}
		}

		// Interpolação de mensagem interativa
		if sendData.InteractiveMessage != nil {
			interactive = sendData.InteractiveMessage.Interactive

			replaceValues := map[string]interface{}{
				"contact_name":    contact.Name,
				"protocol_number": contact.CurrentTicket.Protocol,
				"contact_number":  contact.Data.Number,
			}

			if interactive.Header != nil {
				if interactive.Header.Text != "" {
					interactive.Header.Text = common.Interpolate(interactive.Header.Text, replaceValues)
				}
			}

			if interactive.Body != nil {
				interactive.Body.Text = common.Interpolate(interactive.Body.Text, replaceValues)
			}

			if interactive.Footer != nil {
				interactive.Footer.Text = common.Interpolate(interactive.Footer.Text, replaceValues)
			}

		}

		message = &models.Message{
			Type:       messageType,
			Origin:     origin,
			ContactId:  contact.Id,
			ServiceId:  contact.ServiceId,
			AccountId:  contact.AccountId,
			Text:       text,
			Timestamp:  &now,
			Visible:    true,
			IsFromMe:   true,
			IsFromSync: false,
			Sent:       true,
			Data: &models.MessageData{
				IsNew:          true,
				Ack:            0,
				IsFirst:        isFirst,
				DontOpenTicket: sendData.DontOpenTicket,
				HsmParameters:  sendData.Parameters,
				Interactive:    interactive,
			},
			QuotedMessageId:    sendData.QuotedMessageId,
			TicketId:           contact.CurrentTicketId,
			UserId:             userId,
			TicketUserId:       ticketUserId,
			TicketDepartmentId: ticketDepartmentId,
		}

		if sendData.HsmId != uuid.Nil {
			template, err := m.templateRepository.FindById(ctx, sendData.HsmId, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage find template failed", slog.String("error", err.Error()), slog.String("sendData.HsmId", sendData.HsmId.String()))
				return nil, fmt.Errorf("SendMessage find template failed: %w", err)
			}

			message.HsmId = template.Id

			//@TODO: verificar se é type mídia
			//@TODO: migrar frontend para duplicar file padrão antes de enviar, ai só envia o fileId também e não precisa desse bloco de codigo
			if sendData.FileId == uuid.Nil {
				if sendData.HsmFileId != uuid.Nil {
					message.HsmFileId = sendData.HsmFileId
				} else {
					fileExample, err := m.fileRepository.FindOne(ctx,
						repositories.WithQueryStruct(map[string]interface{}{
							"attachedId":   template.Id,
							"accountId":    contact.AccountId,
							"attachedType": "hsm.file",
						}),
						repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
							return db.Order(`"createdAt" desc`)
						}))

					if err != nil && err.Error() != "record not found" {
						slog.ErrorContext(ctx, "SendMessage find file failed", slog.String("error", err.Error()), slog.String("template.Id", template.Id.String()))
						return nil, fmt.Errorf("SendMessage find file failed: %w", err)
					}

					if fileExample != nil {
						message.HsmFileId = fileExample.Id
					}
				}
			}
		}

		err = m.messageRepository.Create(ctx, message, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "SendMessage create message failed", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("SendMessage create message failed: %w", err)
		}

		err = m.eventService.Dispatch(ctx, event.EventMessageCreated, message, &event.EventServiceDispatchOptions{
			Etx: etx,
		})

		if err != nil {
			slog.ErrorContext(ctx, "SendMessage dispatch message created event failed", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("SendMessage dispatch message created event failed: %w", err)
		}

		if sendData.Text != "" {
			err = m.messageLinkService.ProcessMessageLinks(ctx, message.Id, sendData.Text, tx)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to process message links", slog.String("error", err.Error()), slog.String("messageId", message.Id.String()))
				return nil, err
			}
		}

		if sendData.StickerId != uuid.Nil && message.UserId != uuid.Nil {
			err = m.stickerService.ProcessStickerUsage(ctx, sendData.StickerId, message.UserId, message.AccountId, tx)
			if err != nil {
				slog.ErrorContext(ctx, "SendMessage process sticker usage failed", slog.String("error", err.Error()), slog.String("stickerId", sendData.StickerId.String()), slog.String("userId", message.UserId.String()))
				return nil, fmt.Errorf("SendMessage process sticker usage failed: %w", err)
			}
		}

		if message.Type == "schedule" || (origin == "campaign" && sendData.DepartmentId != uuid.Nil) {
			campaignData = &ticket.CampaignData{
				DepartmentId: sendData.DepartmentId,
				UserId:       sendData.UserId,
			}
		}

		err = m.ticketService.HandleMessageCreated(ctx, message, campaignData, tx, etx)

		if err != nil {
			slog.ErrorContext(ctx, "SendMessage handle message created failed", slog.String("error", err.Error()), slog.Any("message", message), slog.Any("campaignData", campaignData))
			return nil, fmt.Errorf("SendMessage handle message created failed: %w", err)
		}

		if origin == "campaign" && sendData.CampaignMessageProgressId != uuid.Nil {
			campaignMessageProgress := &models.CampaignMessageProgress{
				MessageId: message.Id,
				SentAt:    &now,
			}

			err = m.campaignMessageProgressRepository.UpdateById(ctx, sendData.CampaignMessageProgressId, campaignMessageProgress, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage update campaign message progress failed", slog.String("error", err.Error()), slog.Any("campaignMessageProgress", campaignMessageProgress))
				return nil, fmt.Errorf("SendMessage update campaign message progress failed: %w", err)
			}
		}
		var unread int

		if origin != "bot" {
			unread = 0
		}

		contact, err := m.contactRepository.FindById(ctx, message.ContactId, repositories.WithTx(tx))
		if err != nil {
			slog.ErrorContext(ctx, "SendMessage find contact failed", slog.String("error", err.Error()), slog.Any("message.ContactId", message.ContactId))
			return nil, fmt.Errorf("SendMessage find contact failed: %w", err)
		}

		contact.LastMessageAt = message.Timestamp
		contact.LastMessageId = message.Id
		contact.Unread = unread
		contact.Visible = true // Necessário para contatos que eram de grupos e foram "respondidos no privado"

		err = m.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "SendMessage update contact failed", slog.String("error", err.Error()), slog.Any("message.ContactId", message.ContactId), slog.Any("contact", contact))
			return nil, fmt.Errorf("SendMessage update contact failed: %w", err)
		}

		err = m.eventService.Dispatch(ctx, event.EventContactUpdated, contact, &event.EventServiceDispatchOptions{
			DebounceKey: contact.Id.String(),
			Etx:         etx,
		})

		if err != nil {
			slog.ErrorContext(ctx, "SendMessage dispatch contact updated event failed", slog.String("error", err.Error()), slog.Any("contact", contact))
			return nil, fmt.Errorf("SendMessage dispatch contact updated event failed: %w", err)
		}

		// Retrocompatibilidade
		// File padrão já existente no banco e storage, pode ser apenas clonado
		if messageType == "interactive" && sendData.InteractiveMessage != nil && sendData.InteractiveMessage.File != nil && sendData.InteractiveMessage.File.Id != uuid.Nil && sendData.FileId == uuid.Nil {
			// Arquivo padrão, clona arquivo
			file, err = m.fileRepository.FindById(ctx, sendData.InteractiveMessage.File.Id, repositories.WithTx(tx))
			if err != nil {
				slog.ErrorContext(ctx, "Failed to find file by Id", slog.String("FileId", sendData.InteractiveMessage.File.Id.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to find file by Id: %w", err)
			}

			clonedFile := &models.File{
				AttachedId:   message.Id,
				AttachedType: "message.file",
				AccountId:    message.AccountId,
				Name:         file.Name,
				Mimetype:     file.Mimetype,
				Extension:    file.Extension,
				Storage:      file.Storage,
				Checksum:     file.Checksum,
			}

			err := m.fileRepository.Create(ctx, clonedFile, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "Failed to create file", slog.Any("clonedFile", clonedFile), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to create file: %w", err)
			}

			err = m.storageService.Clone(ctx, file, clonedFile)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to clone file content", slog.String("sourceFileId", file.Id.String()), slog.String("clonedFileId", clonedFile.Id.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to clone file content: %w", err)
			}
		}

		// Retrocompatibilidade
		// File padrão já existente no banco e storage, pode ser apenas clonado
		if messageType == "hsm" && message.HsmFileId != uuid.Nil && sendData.FileId == uuid.Nil {
			file, err = m.fileRepository.FindById(ctx, message.HsmFileId, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "Failed to find file by Id", slog.String("FileId", message.HsmFileId.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to find file by Id: %w", err)
			}

			clonedFile := &models.File{
				AttachedId:   message.Id,
				AttachedType: "message.file",
				AccountId:    message.AccountId,
				Name:         file.Name,
				Mimetype:     file.Mimetype,
				Extension:    file.Extension,
				Storage:      file.Storage,
				Checksum:     file.Checksum,
			}

			err := m.fileRepository.Create(ctx, clonedFile, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "Failed to create file", slog.Any("clonedFile", clonedFile), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to create file: %w", err)
			}

			err = m.storageService.Clone(ctx, file, clonedFile)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to clone file content", slog.String("sourceFileId", file.Id.String()), slog.String("clonedFileId", clonedFile.Id.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to clone file content: %w", err)
			}
		}

		// File criado pelo próprio front, só vem o id
		if sendData.FileId != uuid.Nil {
			fileUrl, err := m.storageService.GetPresignedUrl(ctx, file, nil)

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage get presigned url failed", slog.String("error", err.Error()), slog.Any("file", file))
				return nil, fmt.Errorf("SendMessage get presigned url failed: %w", err)
			}

			resp, err := http.Get(fileUrl)

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage http get file failed", slog.String("error", err.Error()), slog.String("fileUrl", fileUrl))
				return nil, fmt.Errorf("SendMessage http get file failed: %w", err)
			}

			defer func() {
				if err := resp.Body.Close(); err != nil {
					fmt.Println("Error closing response body:", err)
				}
			}()

			data, err := io.ReadAll(resp.Body)

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage read response body failed", slog.String("error", err.Error()), slog.String("fileUrl", fileUrl))
				return nil, fmt.Errorf("SendMessage read response body failed: %w", err)
			}

			checksum, err := common.GetChecksum(bytes.NewReader(data))

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage get checksum from data failed", slog.String("error", err.Error()), slog.String("fileUrl", fileUrl))
				return nil, fmt.Errorf("SendMessage get checksum from data failed: %w", err)
			}

			file := &models.File{
				AttachedId: message.Id,
				Checksum:   checksum,
			}

			err = m.fileRepository.UpdateById(ctx, sendData.FileId, file, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage update file failed", slog.String("error", err.Error()), slog.Any("sendData.FileId", sendData.FileId), slog.Any("file", file))
				return nil, fmt.Errorf("SendMessage update file failed: %w", err)
			}
		}

		if (origin == "user" || origin == "schedule") && contact.Data.Survey != nil {
			_, err = m.answerService.UpdateAnswerForContact(ctx, contact, nil, "-1", tx)

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage update answer for contact failed", slog.String("error", err.Error()), slog.Any("contact", contact))
				return nil, fmt.Errorf("SendMessage update answer for contact failed: %w", err)
			}

			err = m.answerService.UnflagSurveyFromContact(ctx, contact, tx, etx)

			if err != nil {
				slog.ErrorContext(ctx, "SendMessage unflag survey from contact failed", slog.String("error", err.Error()), slog.Any("contact", contact))
				return nil, fmt.Errorf("SendMessage unflag survey from contact failed: %w", err)
			}
		}

		err = etx.Commit()

		if err != nil {
			slog.ErrorContext(ctx, "Send message failed to commit event transaction", slog.String("error", err.Error()))
			return nil, fmt.Errorf("send message failed to commit event transaction: %w", err)
		}

		return nil, err
	})

	if err != nil {
		slog.ErrorContext(ctx, "SendMessage failed", slog.String("err", err.Error()), slog.Any("sendData", sendData))
		return nil, fmt.Errorf("SendMessage failed: %w", err)
	}

	if message.IsComment {
		return message, nil
	}

	err = m.queueDispatcher.Dispatch(
		ctx,
		"send-message-to-broker",
		&payloads.SendMessageToBrokerPayloadPayload{
			MessageId: message.Id,
			ServiceId: contact.ServiceId,
			AccountId: contact.AccountId,
		},
		&queueDispatcher.QueueJobsDispatcherDispatchOptions{
			HashKey: contact.AccountId.String() + "_" + contact.ServiceId.String() + "_" + contact.Id.String(),
		},
	)

	if err != nil {
		if message != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker failed", slog.String("error", err.Error()), slog.String("messageId", message.Id.String()), slog.String("serviceId", contact.ServiceId.String()), slog.String("accountId", contact.AccountId.String()))
		} else {
			slog.ErrorContext(ctx, "SendMessageToBroker failed", slog.String("error", err.Error()), slog.String("serviceId", contact.ServiceId.String()), slog.String("accountId", contact.AccountId.String()))
		}

		return nil, fmt.Errorf("SendMessageToBroker failed: %w", err)
	}

	if contact.Service.Type == "whatsapp-business" && message.HsmId != uuid.Nil {
		err = m.accountService.IncrementHsmUsedLimit(ctx, contact.AccountId)

		if err != nil {
			slog.ErrorContext(ctx, "SendMessage increment hsm used limit failed", slog.String("error", err.Error()), slog.String("accountId", contact.AccountId.String()))
			return nil, fmt.Errorf("SendMessage increment hsm used limit failed: %w", err)
		}
	}

	return message, nil
}

func (m *SendService) CanSend(ctx context.Context, message *models.Message) (err error) {
	if message.Service.Type == "whatsapp-business" && message.HsmId != uuid.Nil {
		if !message.Account.Settings.Flags.DisableHsmLimit {

			limit := message.Account.Plan.HsmLimit

			if limit <= 0 {
				limit = m.Config.DefaultHsmLimit
			}

			if message.Account.Plan.HsmUsedLimit >= limit {
				return fmt.Errorf("hsm limit exceeded")
			}
		}
	}
	return nil
}

func (m *SendService) GetFileUrl(ctx context.Context, file *models.File) (fileUrl string, err error) {
	if file == nil {
		return "", nil
	}

	if file.Storage == "" {
		slog.ErrorContext(ctx, "GetFileUrl storage not found", slog.String("error", "storage not found"), slog.Any("file", file))
		return "", errors.New("storage not found")
	}

	err = file.AfterFind(nil)

	if err != nil {
		slog.ErrorContext(ctx, "GetFileUrl after find failed", slog.String("error", err.Error()), slog.Any("file", file))
		return "", fmt.Errorf("GetFileUrl after find failed: %w", err)
	}

	url, err := m.storageService.GetPresignedUrl(ctx, file, nil)

	if err != nil {
		slog.ErrorContext(ctx, "GetFileUrl get presigned url failed", slog.String("error", err.Error()), slog.Any("file", file))
		return "", fmt.Errorf("GetFileUrl get presigned url failed: %w", err)
	}

	return url, nil

}

func (m *SendService) SendMessageToBroker(ctx context.Context, sendData *payloads.SendMessageToBrokerPayloadPayload) (message *models.Message, err error) {
	message, err = m.messageRepository.FindById(ctx, sendData.MessageId, repositories.WithRelations(
		"Service",
		"File",
		"Account",
		"Contact",
		"WhatsappBusinessTemplate",
	))

	if err != nil {
		slog.ErrorContext(ctx, "SendMessageToBroker find message failed", slog.String("error", err.Error()), slog.Any("sendData", sendData))
		return nil, fmt.Errorf("SendMessageToBroker find message failed: %w", err)
	}

	err = m.CanSend(ctx, message)

	if err != nil {
		slog.ErrorContext(ctx, "SendMessageToBroker can send failed", slog.String("error", err.Error()), slog.Any("sendData", sendData))
		return nil, fmt.Errorf("SendMessageToBroker can send failed: %w", err)
	}

	adapter, err := m.adapterManager.GetAdapter(ctx, message.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "SendMessageToBroker get adapter failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.String("message.ServiceId", message.ServiceId.String()))
		return nil, fmt.Errorf("SendMessageToBroker get adapter failed: %w", err)
	}

	fileUrl, err := m.GetFileUrl(ctx, message.File)

	if err != nil {
		slog.ErrorContext(ctx, "SendMessageToBroker get file url failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.Any("message.File", message.File))
		return nil, fmt.Errorf("SendMessageToBroker get file url failed: %w", err)
	}

	text := ""

	if message.Text != "" {
		text, err = m.accountCryptor.DecryptTextForAccount(ctx, message.Account, message.Text, false)

		if err != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker decrypt text failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.Any("message.Account", message.Account), slog.String("message.Text", message.Text))
			return nil, fmt.Errorf("SendMessageToBroker decrypt text failed: %w", err)
		}
	}

	var replyMessageId string

	if message.QuotedMessageId != uuid.Nil {
		replyMessage, err := m.messageRepository.FindById(ctx, message.QuotedMessageId)

		if err != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker find reply message failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.String("message.QuotedMessageId", message.QuotedMessageId.String()))
			return nil, fmt.Errorf("SendMessageToBroker find reply message failed: %w", err)
		}

		replyMessageId = replyMessage.IdFromService
	}

	var sendMessageResponse *adapter_types.SendMessageResponse

	queueKey, err := lock.GetContactLockKey(sendData.AccountId, sendData.ServiceId, message.ContactId)

	if err != nil {
		slog.ErrorContext(ctx, "SendMessageToBroker get contact lock key failed", slog.String("error", err.Error()), slog.String("sendData.AccountId", sendData.AccountId.String()), slog.String("sendData.ServiceId", sendData.ServiceId.String()), slog.String("message.ContactId", message.ContactId.String()))
		return nil, fmt.Errorf("SendMessageToBroker get contact lock key failed: %w", err)
	}

	queue := m.distributedLockFactory(queueKey, 2*time.Minute)

	_, err = queue.Run(ctx, func() (interface{}, error) {
		switch message.Type {
		case "sticker":
			sendMessageResponse, err = adapter.SendSticker(ctx, message.ServiceId, &adapter_types.SendStickerPayload{
				To:             message.Contact.IdFromService,
				ReplyMessageId: replyMessageId,
				Url:            fileUrl,
			})
		case "interactive":
			sendMessageResponse, err = adapter.SendInteractiveMessage(ctx, message.ServiceId, &adapter_types.SendInteractiveMessagePayload{
				To:             message.Contact.IdFromService,
				ReplyMessageId: replyMessageId,
				Interactive:    message.Data.Interactive,
				Url:            fileUrl,
			})
		case "hsm":
			{
				payload := &adapter_types.SendTemplatePayload{
					To:             message.Contact.IdFromService,
					ReplyMessageId: replyMessageId,
					Url:            fileUrl,
					Template:       message.WhatsappBusinessTemplate,
					Parameters:     message.Data.HsmParameters,
				}

				// @TODO: Mudar a logica para um lugar melhor, fiz aqui só para testar
				if m.Config.UseMockDriver {
					response, err := m.httpJobsDispatcherService.DispatchToMockDriver(ctx, "/v1/meta/messages/send",
						map[string]interface{}{
							"payload":    payload,
							"webhookUrl": m.Config.ApiPublicUrl + "/whatsapp-business-webhook/" + sendData.ServiceId.String(),
						},
					)

					messages := response["messages"].([]interface{})

					if len(messages) == 0 {
						return nil, fmt.Errorf("invalid sending template response")
					}

					messageResp := messages[0].(map[string]interface{})

					now := time.Now()
					sendMessageResponse = &adapter_types.SendMessageResponse{
						MessageId: messageResp["id"].(string),
						Timestamp: &now,
						Ack:       "0",
						Error:     nil,
					}
					if err != nil {
						return nil, err
					}
				} else {
					sendMessageResponse, err = adapter.SendTemplate(ctx, message.ServiceId, payload)
				}
			}
		case "chat":
			payload := &adapter_types.SendTextPayload{
				To:             message.Contact.IdFromService,
				Text:           text,
				ReplyMessageId: replyMessageId,
			}

			// @TODO: Mudar a logica para um lugar melhor, fiz aqui só para testar
			if m.Config.UseMockDriver {
				response, err := m.httpJobsDispatcherService.DispatchToMockDriver(ctx, "/v1/meta/messages/send",
					map[string]interface{}{
						"payload":    payload,
						"webhookUrl": m.Config.ApiPublicUrl + "/whatsapp-business-webhook/" + sendData.ServiceId.String(),
					},
				)

				messages := response["messages"].([]interface{})

				if len(messages) == 0 {
					return nil, fmt.Errorf("invalid sending template response")
				}

				messageResp := messages[0].(map[string]interface{})

				now := time.Now()
				sendMessageResponse = &adapter_types.SendMessageResponse{
					MessageId: messageResp["id"].(string),
					Timestamp: &now,
					Ack:       "0",
					Error:     nil,
				}
				if err != nil {
					return nil, err
				}
			} else {
				sendMessageResponse, err = adapter.SendText(ctx, message.ServiceId, payload)
			}

		case "audio":
			sendMessageResponse, err = adapter.SendAudio(ctx, message.ServiceId, &adapter_types.SendAudioPayload{
				To:             message.Contact.IdFromService,
				Url:            fileUrl,
				Caption:        text,
				Mimetype:       message.File.Mimetype,
				ReplyMessageId: replyMessageId,
			})
		case "image":
			sendMessageResponse, err = adapter.SendImage(ctx, message.ServiceId, &adapter_types.SendImagePayload{
				To:       message.Contact.IdFromService,
				Url:      fileUrl,
				Caption:  text,
				Filename: message.File.Name,
				Mimetype: message.File.Mimetype,
			})
		case "video":
			sendMessageResponse, err = adapter.SendVideo(ctx, message.ServiceId, &adapter_types.SendVideoPayload{
				To:       message.Contact.IdFromService,
				Url:      fileUrl,
				Caption:  text,
				Filename: message.File.Name,
				Mimetype: message.File.Mimetype,
			})
		case "document":
			sendMessageResponse, err = adapter.SendDocument(ctx, message.ServiceId, &adapter_types.SendDocumentPayload{
				To:       message.Contact.IdFromService,
				Url:      fileUrl,
				Caption:  text,
				Filename: message.File.Name,
				Mimetype: message.File.Mimetype,
			})
		default:
			err = errors.New("invalid message type " + message.Type)
		}
		if err != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker adapter send failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.Any("sendMessageReponse", sendMessageResponse), slog.String("message.Type", message.Type))
			return nil, fmt.Errorf("SendMessageToBroker adapter send failed: %w", err)
		}

		if sendMessageResponse == nil || sendMessageResponse.Error == nil && sendMessageResponse.MessageId == "" {
			slog.ErrorContext(ctx, "SendMessageToBroker response messageId and error is empty", slog.Any("sendMessageResponse", sendMessageResponse), slog.Any("sendData", sendData), slog.String("message.Type", message.Type))
			return nil, fmt.Errorf("SendMessageToBroker response messageId and error is empty: %w", errors.New("response messageId and error is empty"))
		}

		if sendMessageResponse.MessageId != "" && sendMessageResponse.Timestamp == nil {
			slog.ErrorContext(ctx, "SendMessageToBroker response timestamp is empty", slog.Any("sendMessageReponse", sendMessageResponse), slog.Any("sendData", sendData), slog.String("message.Type", message.Type))
			return nil, fmt.Errorf("SendMessageToBroker response timestamp is empty: %w", errors.New("response timestamp is empty"))
		}

		tx := m.messageRepository.BeginTransaction()
		defer tx.Finish(&err)

		message, err := m.messageRepository.FindById(ctx, sendData.MessageId, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker find message failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.String("message.Id", sendData.MessageId.String()))
			return nil, fmt.Errorf("SendMessageToBroker find message failed: %w", err)
		}

		message.IdFromService = sendMessageResponse.MessageId
		message.Timestamp = sendMessageResponse.Timestamp
		message.Data.Ack = sendMessageResponse.Ack
		message.Data.Error = sendMessageResponse.Error

		err = m.messageRepository.UpdateById(ctx, message.Id, message, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker update message failed", slog.String("error", err.Error()), slog.Any("sendData", sendData), slog.Any("message", message))
			return nil, fmt.Errorf("SendMessageToBroker update message failed: %w", err)
		}

		err = m.eventService.Dispatch(ctx, event.EventMessageUpdated, message, &event.EventServiceDispatchOptions{
			DebounceKey: message.Id.String(),
		})

		if err != nil {
			slog.ErrorContext(ctx, "SendMessageToBroker dispatch message updated event failed", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("SendMessageToBroker dispatch message updated event failed: %w", err)
		}

		return nil, nil
	})

	if err != nil {
		if message.Service.Type == "whatsapp-business" && message.HsmId != uuid.Nil {
			err = m.accountService.DecrementHsmUsedLimit(ctx, message.AccountId)

			if err != nil {
				slog.ErrorContext(ctx, "SendMessageToBroker decrement hsm used limit failed", slog.String("error", err.Error()), slog.String("accountId", message.AccountId.String()))
				return nil, fmt.Errorf("SendMessageToBroker decrement hsm used limit failed: %w", err)
			}
		}
		slog.ErrorContext(ctx, "SendMessageToBroker queue run failed", slog.String("error", err.Error()), slog.Any("sendData", sendData))
		return nil, fmt.Errorf("SendMessageToBroker queue run failed: %w", err)
	}

	return message, nil
}
