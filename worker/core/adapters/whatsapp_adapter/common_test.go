//go:build unit

package whatsapp_adapter

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

/*
 * Test function: getWebhookMessageType
 */
type WhatsappGetWebhookMessageTypeTestSuite struct {
	suite.Suite
}

func (suite *WhatsappGetWebhookMessageTypeTestSuite) TestValidMessageTypes() {
	tests := []struct {
		testName    string
		messageType WebhookMessageTypeEnum
		expected    string
	}{
		{
			testName:    "message type location",
			messageType: "location",
			expected:    "location",
		},
		{
			testName:    "message type vcard",
			messageType: "vcard",
			expected:    "vcard",
		},
		{
			testName:    "message type multi_vcard",
			messageType: "multi_vcard",
			expected:    "vcard",
		},
		{
			testName:    "message type ptt",
			messageType: "ptt",
			expected:    "audio",
		},
		{
			testName:    "message type audio",
			messageType: "audio",
			expected:    "audio",
		},
		{
			testName:    "message type image",
			messageType: "image",
			expected:    "image",
		},
		{
			testName:    "message type video",
			messageType: "video",
			expected:    "video",
		},
		{
			testName:    "message type document",
			messageType: "document",
			expected:    "document",
		},
		{
			testName:    "message type sticker",
			messageType: "sticker",
			expected:    "sticker",
		},
		{
			testName:    "message type chat",
			messageType: "chat",
			expected:    "chat",
		},
		{
			testName:    "message type e2e_notification",
			messageType: "e2e_notification",
			expected:    "e2e_notification",
		},
		{
			testName:    "message type notification_template",
			messageType: "notification_template",
			expected:    "e2e_notification",
		},
		{
			testName:    "message type gp2",
			messageType: "gp2",
			expected:    "e2e_notification",
		},
		{
			testName:    "message type event_creation",
			messageType: "event_creation",
			expected:    "event_creation",
		},
	}

	for _, testCase := range tests {
		suite.Run(testCase.testName, func() {
			messagePayload := &WebhookMessagePayload{
				Type: WebhookMessageTypeEnum(testCase.messageType),
			}

			result, err := getWebhookMessageType(context.Background(), messagePayload)

			suite.Require().NoError(err)
			suite.Require().Equal(testCase.expected, result)
		})
	}
}

func (suite *WhatsappGetWebhookMessageTypeTestSuite) TestInvalidMessageTypes() {
	tests := []struct {
		testName    string
		messageType string
	}{
		{
			testName:    "message unknown type",
			messageType: "unknown",
		},
		{
			testName:    "message unknown type group_chat_created",
			messageType: "group_chat_created",
		},
	}

	for _, testCase := range tests {
		suite.Run(testCase.testName, func() {
			messagePayload := &WebhookMessagePayload{
				Type: WebhookMessageTypeEnum(testCase.messageType),
			}

			result, err := getWebhookMessageType(context.Background(), messagePayload)

			suite.Require().EqualError(err, fmt.Sprintf("webhook message type is unknown: %s", testCase.messageType))
			suite.Require().Equal("", result)
		})
	}
}

func TestWhatsappGetWebhookMessageTypeTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappGetWebhookMessageTypeTestSuite))
}

/*
 * Test function: getWebhookType
 */
type WhatsappGetWebhookTypeTestSuite struct {
	suite.Suite
}

func (suite *WhatsappGetWebhookTypeTestSuite) TestValidEventTypes() {
	tests := []struct {
		testName  string
		eventType Event
		expected  string
	}{
		{
			testName:  "event type service.updated",
			eventType: "service.updated",
			expected:  "service",
		},
		{
			testName:  "event type whatsapp.message.created",
			eventType: "whatsapp.message.created",
			expected:  "message",
		},
		{
			testName:  "event type whatsapp.message.updated",
			eventType: "whatsapp.message.updated",
			expected:  "message",
		},
		{
			testName:  "event type whatsapp.message.reaction",
			eventType: "whatsapp.message.reaction",
			expected:  "reaction",
		},
	}

	for _, testCase := range tests {
		suite.Run(testCase.testName, func() {
			webhookPayload := &WhatsappWebhookPayload{
				Payload: WhatsappWebhookPayloadBody{
					Event: Event(testCase.eventType),
				},
			}

			result, err := getWebhookType(context.Background(), webhookPayload)

			suite.Require().NoError(err)
			suite.Require().Equal(testCase.expected, result)
		})
	}
}

func (suite *WhatsappGetWebhookTypeTestSuite) TestInvalidEventTypes() {
	tests := []struct {
		testName  string
		eventType Event
	}{
		{
			testName:  "event type message.created",
			eventType: "message.created",
		},
		{
			testName:  "event type message.updated",
			eventType: "message.updated",
		},
	}

	for _, testCase := range tests {
		suite.Run(testCase.testName, func() {
			webhookPayload := &WhatsappWebhookPayload{
				Payload: WhatsappWebhookPayloadBody{
					Event: Event(testCase.eventType),
				},
			}

			result, err := getWebhookType(context.Background(), webhookPayload)

			suite.Require().EqualError(err, fmt.Sprintf("unknown webhook event type %s", testCase.eventType))
			suite.Require().Equal("", result)
		})
	}
}

func TestWhatsappGetWebhookTypeTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappGetWebhookTypeTestSuite))
}

func TestWhatsappGetContactName(t *testing.T) {
	name := "Nome na agenda ou grupo"
	number := "5514999999999"
	profileName := "Meu nome no WhatsApp"

	tests := []struct {
		testName string
		contact  *Contact
		expected string
	}{
		{
			testName: "test with group name",
			contact:  &Contact{Name: name, Number: number, ProfileName: profileName, IsGroup: true},
			expected: name,
		},
		{
			testName: "test with empty group name",
			contact:  &Contact{Name: "", Number: number, ProfileName: profileName, IsGroup: true},
			expected: "",
		},
		{
			testName: "test with contact profile name",
			contact:  &Contact{Name: name, Number: number, ProfileName: profileName, IsGroup: false},
			expected: profileName,
		},
		{
			testName: "test with contact name",
			contact:  &Contact{Name: name, Number: number, ProfileName: "", IsGroup: false},
			expected: name,
		},
		{
			testName: "test with contact number",
			contact:  &Contact{Name: "", Number: number, ProfileName: "", IsGroup: false},
			expected: number,
		},
		{
			testName: "test with empty names",
			contact:  &Contact{Name: "", Number: "", ProfileName: "", IsGroup: false},
			expected: "",
		},
	}

	for _, testCase := range tests {
		t.Run(testCase.testName, func(t *testing.T) {
			result := getContactName(testCase.contact)

			require.Equal(t, testCase.expected, result)
		})
	}
}

func TestWhatsappGetContactAlternativeName(t *testing.T) {
	name := "Nome na agenda ou grupo"
	number := "5514999999999"
	profileName := "Meu nome no WhatsApp"

	tests := []struct {
		testName string
		contact  *Contact
		expected string
	}{
		{
			testName: "test with group name",
			contact:  &Contact{Name: name, Number: number, ProfileName: profileName, IsGroup: true},
			expected: "",
		},
		{
			testName: "test with empty group name",
			contact:  &Contact{Name: "", Number: number, ProfileName: profileName, IsGroup: true},
			expected: "",
		},
		{
			testName: "test with contact profile name",
			contact:  &Contact{Name: name, Number: number, ProfileName: profileName, IsGroup: false},
			expected: name,
		},
		{
			testName: "test with contact name",
			contact:  &Contact{Name: name, Number: number, ProfileName: "", IsGroup: false},
			expected: name,
		},
		{
			testName: "test with contact number",
			contact:  &Contact{Name: "", Number: number, ProfileName: profileName, IsGroup: false},
			expected: number,
		},
		{
			testName: "test with empty names",
			contact:  &Contact{Name: "", Number: "", ProfileName: "", IsGroup: false},
			expected: "",
		},
	}

	for _, testCase := range tests {
		t.Run(testCase.testName, func(t *testing.T) {
			result := getContactAlternativeName(testCase.contact)

			require.Equal(t, testCase.expected, result)
		})
	}
}
