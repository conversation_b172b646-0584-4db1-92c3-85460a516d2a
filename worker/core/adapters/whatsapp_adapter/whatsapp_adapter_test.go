//go:build unit

package whatsapp_adapter

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/http/payloads"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

/*
 * Test function: BuildWebhook
 */
type WhatsappAdapterBuildWebhookTestSuite struct {
	suite.Suite
	whatsappAdapter *WhatsappAdapter
}

func (suite *WhatsappAdapterBuildWebhookTestSuite) SetupSuite() {
	suite.whatsappAdapter = &WhatsappAdapter{}
}

func (suite *WhatsappAdapterBuildWebhookTestSuite) TestInvalidPayload() {
	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, "")

	suite.Require().ErrorContains(err, "failed to convert payload:")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookTestSuite) TestUnknownEvent() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "unknown_event",
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().EqualError(err, "failed to get webhook type: unknown webhook event type unknown_event")
	suite.Require().Equal(0, len(result))
}

func TestWhatsappAdapterBuildWebhookTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterBuildWebhookTestSuite))
}

/*
 * Test function: BuildWebhook
 * Webhook type: Message
 */
type WhatsappAdapterBuildWebhookMessageTestSuite struct {
	suite.Suite
	whatsappAdapter *WhatsappAdapter
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) SetupSuite() {
	suite.whatsappAdapter = &WhatsappAdapter{}
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestInvalidMessagePayload() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data":  "",
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().ErrorContains(err, "failed to convert payload data to slice of WebhookMessagePayload")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestEmptyMessagePayload() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data":  map[string]interface{}{},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().ErrorContains(err, "failed to build message single")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestInvalidMessageType() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": map[string]interface{}{
				"Type": "unknown",
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().ErrorContains(err, "failed to build message single")
	suite.Require().ErrorContains(err, "failed to get webhook message type: webhook message type is unknown: unknown")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestSingleMessageValid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": map[string]interface{}{
				"Type":    "chat",
				"Text":    "Hello world",
				"Data":    map[string]interface{}{},
				"Contact": map[string]interface{}{},
				"From":    map[string]interface{}{},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(1, len(result))
	suite.Require().Equal("chat", result[0].Message.Type)
	suite.Require().Equal("Hello world", result[0].Message.Text)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageEmpty() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageInvalidType() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type": "unknown",
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageValid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Hello world",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Nice to meet you",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(2, len(result))
	suite.Require().Equal("chat", result[0].Message.Type)
	suite.Require().Equal("Hello world", result[0].Message.Text)
	suite.Require().Equal("chat", result[1].Message.Type)
	suite.Require().Equal("Nice to meet you", result[1].Message.Text)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageSomeInvalid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type": "unknown",
				},
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Hello world",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type": "unknown",
				},
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Nice to meet you",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(2, len(result))
	suite.Require().Equal("chat", result[0].Message.Type)
	suite.Require().Equal("Hello world", result[0].Message.Text)
	suite.Require().Equal("chat", result[1].Message.Type)
	suite.Require().Equal("Nice to meet you", result[1].Message.Text)
}

func TestWhatsappAdapterBuildWebhookMessageTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterBuildWebhookMessageTestSuite))
}

func TestWhatsappAdapterBuildContact(t *testing.T) {
	id := "<EMAIL>"
	name := "Nome na agenda ou grupo"
	number := "5514999999999"
	profileName := "Meu nome no WhatsApp"
	avatarUrl := "https://digisac.com.br/"

	tests := []struct {
		testName string
		contact  *Contact
		expected *adapter_types.WebhookContact
	}{
		{
			testName: "test with contact nil",
			contact:  nil,
			expected: nil,
		},
		{
			testName: "test with contact empty",
			contact:  &Contact{},
			expected: &adapter_types.WebhookContact{Visible: true},
		},
		{
			testName: "test with contact fill",
			contact:  &Contact{Id: id, Name: name, Number: number, ProfileName: profileName, IsGroup: false, IsMe: false, AvatarUrl: avatarUrl},
			expected: &adapter_types.WebhookContact{Id: id, Name: profileName, AlternativeName: name, IsGroup: false, IsMe: false, Visible: true, AvatarUrl: avatarUrl},
		},
	}

	whatsappAdapter := &WhatsappAdapter{}

	for _, testCase := range tests {
		t.Run(testCase.testName, func(t *testing.T) {
			result, err := whatsappAdapter.BuildContact(testCase.contact)

			require.NoError(t, err)
			require.Equal(t, testCase.expected, result)
		})
	}
}

func TestWhatsappAdapterBuildFrom(t *testing.T) {
	id := "<EMAIL>"
	name := "Nome na agenda ou grupo"
	number := "5514999999999"
	profileName := "Meu nome no WhatsApp"
	avatarUrlFrom := "https://digisac.com.br/"
	avatarUrlContact := "https://ikatec.com.br/"

	tests := []struct {
		testName string
		from     *Contact
		contact  *Contact
		expected *adapter_types.WebhookContact
	}{
		{
			testName: "test with contact empty",
			from:     &Contact{},
			contact:  &Contact{},
			expected: &adapter_types.WebhookContact{},
		},
		{
			testName: "test with contact fill",
			from:     &Contact{Id: id, Name: name, Number: number, ProfileName: profileName, IsGroup: false, IsMe: false, AvatarUrl: avatarUrlFrom},
			contact:  &Contact{Id: id, Name: name, Number: number, ProfileName: profileName, IsGroup: false, IsMe: false, AvatarUrl: avatarUrlContact},
			expected: &adapter_types.WebhookContact{Id: id, Name: profileName, AlternativeName: name, IsGroup: false, IsMe: false, Visible: false, AvatarUrl: avatarUrlContact},
		},
	}

	whatsappAdapter := &WhatsappAdapter{}

	for _, testCase := range tests {
		t.Run(testCase.testName, func(t *testing.T) {
			result, err := whatsappAdapter.BuildFrom(testCase.contact, testCase.contact)

			require.NoError(t, err)
			require.Equal(t, testCase.expected, result)
		})
	}
}
