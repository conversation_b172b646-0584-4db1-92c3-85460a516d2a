//go:build unit
// +build unit

package gupshup_adapter

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	"encoding/json"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestProcessAuthenticationTemplate(t *testing.T) {
	// Criar um adaptador Gupshup para teste
	adapter := &GupshupAdapter{}

	// Criar um serviço para teste
	service := &models.Service{
		Id:        uuid.New(),
		AccountId: uuid.New(),
	}

	// Criar um template de autenticação da Gupshup
	template := &GupshupTemplate{
		Id:           "auth-template-id",
		ElementName:  "auth_template",
		LanguageCode: "pt_BR",
		Status:       "APPROVED",
		Category:     "AUTHENTICATION",
		Data:         "Seu código de verificação é {{1}}.",
		TemplateType: "TEXT",
		WabaId:       "test-waba-id",
	}

	// Processar o template
	processedTemplate := adapter.processTemplate(context.Background(), template, service, service.Id)

	// Verificar os campos básicos
	assert.Equal(t, "auth_template", processedTemplate.Name)
	assert.Equal(t, "pt_BR", processedTemplate.Language)
	assert.Equal(t, models.WhatsappBusinessTemplateStatusApproved, processedTemplate.Status)
	assert.Equal(t, "AUTHENTICATION", processedTemplate.Category)
	assert.Equal(t, models.WhatsappBusinessMessageTypeTextOnly, processedTemplate.MessageType)

	// Verificar os componentes
	assert.Equal(t, 2, len(processedTemplate.Components))

	// Verificar o componente BODY
	bodyComponent := processedTemplate.Components[0]
	assert.Equal(t, adapter_types.ComponentTypeBody, bodyComponent.Type)
	assert.Equal(t, "Seu código de verificação é {{1}}.", bodyComponent.Text)
	assert.Equal(t, []string{"VERIFICATION_CODE"}, bodyComponent.Params)

	// Verificar o componente FOOTER
	footerComponent := processedTemplate.Components[1]
	assert.Equal(t, adapter_types.ComponentTypeFooter, footerComponent.Type)
	assert.Equal(t, "Este código expira em {NUM_MINUTES} minutos.", footerComponent.Text)
}

func TestProcessTemplate(t *testing.T) {
	// Criar dados de teste diretamente no código em vez de ler de arquivos

	// Criar um exemplo de resposta da Gupshup
	gupshupResponse := GupshupTemplatesResponse{
		Templates: []*GupshupTemplate{
			{
				Id:           "template_id_1",
				ElementName:  "welcome_template",
				LanguageCode: "pt_BR",
				Status:       "APPROVED",
				Category:     "MARKETING",
				TemplateType: "TEXT",
				Data:         "Olá! Bem-vindo ao nosso serviço. Estamos felizes em tê-lo conosco.",
				WabaId:       "test-waba-id-1",
				ContainerMeta: `{
					"header": "Bem-vindo",
					"footer": "Atenciosamente, Equipe de Suporte",
					"buttons": [
						{
							"type": "URL",
							"text": "Visite nosso site",
							"url": "https://example.com"
						}
					]
				}`,
			},
			{
				Id:           "template_id_2",
				ElementName:  "order_update",
				LanguageCode: "pt_BR",
				Status:       "APPROVED",
				Category:     "UTILITY",
				TemplateType: "IMAGE",
				Data:         "Seu pedido #{{1}} foi {{2}}. Obrigado por comprar conosco!",
				WabaId:       "test-waba-id-1",
				ContainerMeta: `{
					"header": "Atualização de Pedido",
					"sampleMedia": "sample_media_id_123",
					"mediaId": "media_id_123",
					"footer": "Para mais informações, entre em contato conosco."
				}`,
			},
			{
				Id:           "template_id_3",
				ElementName:  "auth_code",
				LanguageCode: "pt_BR",
				Status:       "APPROVED",
				Category:     "AUTHENTICATION",
				TemplateType: "TEXT",
				Data:         "Seu código de verificação é {{1}}.",
				WabaId:       "test-waba-id-1",
			},
		},
	}

	// Criar templates esperados com componentes em formato de string JSON
	type TemplateWithStringComponents struct {
		Id             string `json:"id"`
		Name           string `json:"name"`
		Language       string `json:"language"`
		Status         string `json:"status"`
		Category       string `json:"category"`
		MessageType    string `json:"messageType"`
		IdGupshup      string `json:"idGupshup"`
		Components     string `json:"components"`
		RejectedReason string `json:"rejectedReason"`
	}

	// Criar componentes esperados para o template de boas-vindas
	welcomeComponents := []*models.WhatsappBusinessComponent{
		{
			Type:   adapter_types.ComponentTypeHeader,
			Text:   "Bem-vindo",
			Format: adapter_types.ComponentFormatText,
		},
		{
			Type: adapter_types.ComponentTypeBody,
			Text: "Olá! Bem-vindo ao nosso serviço. Estamos felizes em tê-lo conosco.",
		},
		{
			Type: adapter_types.ComponentTypeFooter,
			Text: "Atenciosamente, Equipe de Suporte",
		},
		{
			Type: adapter_types.ComponentTypeButtons,
			Buttons: []*models.WhatsappBusinessComponentParameterButton{
				{
					Type: "URL",
					Text: "Visite nosso site",
					URL:  "https://example.com",
				},
			},
		},
	}
	welcomeComponentsJSON, _ := json.Marshal(welcomeComponents)

	// Criar componentes esperados para o template de atualização de pedido
	orderComponents := []*models.WhatsappBusinessComponent{
		{
			Type:   adapter_types.ComponentTypeHeader,
			Format: adapter_types.ComponentFormatImage,
			Example: &models.WhatsappBusinessComponentExample{
				HeaderHandle: []string{"sample_media_id_123"},
			},
		},
		{
			Type: adapter_types.ComponentTypeBody,
			Text: "Seu pedido #{{1}} foi {{2}}. Obrigado por comprar conosco!",
		},
		{
			Type: adapter_types.ComponentTypeFooter,
			Text: "Para mais informações, entre em contato conosco.",
		},
	}
	orderComponentsJSON, _ := json.Marshal(orderComponents)

	// Criar componentes esperados para o template de autenticação
	authComponents := []*models.WhatsappBusinessComponent{
		{
			Type:   adapter_types.ComponentTypeBody,
			Text:   "Seu código de verificação é {{1}}.",
			Params: []string{"VERIFICATION_CODE"},
		},
		{
			Type: adapter_types.ComponentTypeFooter,
			Text: "Este código expira em {NUM_MINUTES} minutos.",
		},
	}
	authComponentsJSON, _ := json.Marshal(authComponents)

	// Criar mapa de templates esperados
	expectedTemplateMap := map[string]*TemplateWithStringComponents{
		"template_id_1": {
			Name:        "welcome_template",
			Language:    "pt_BR",
			Status:      "APPROVED",
			Category:    "MARKETING",
			MessageType: "interactive",
			IdGupshup:   "template_id_1",
			Components:  string(welcomeComponentsJSON),
		},
		"template_id_2": {
			Name:        "order_update",
			Language:    "pt_BR",
			Status:      "APPROVED",
			Category:    "UTILITY",
			MessageType: "interactive",
			IdGupshup:   "template_id_2",
			Components:  string(orderComponentsJSON),
		},
		"template_id_3": {
			Name:        "auth_code",
			Language:    "pt_BR",
			Status:      "APPROVED",
			Category:    "AUTHENTICATION",
			MessageType: "text_only",
			IdGupshup:   "template_id_3",
			Components:  string(authComponentsJSON),
		},
	}

	// Criar um adaptador Gupshup para teste
	adapter := &GupshupAdapter{}

	// Criar um serviço de teste
	service := &models.Service{
		Id:        uuid.New(),
		AccountId: uuid.New(),
	}

	// Processar cada template e verificar se o resultado corresponde ao esperado
	for _, gupshupTemplate := range gupshupResponse.Templates {
		processedTemplate := adapter.processTemplate(context.Background(), gupshupTemplate, service, service.Id)

		// Verificar se existe um template esperado correspondente
		expectedTemplate, exists := expectedTemplateMap[gupshupTemplate.Id]
		if !exists {
			t.Fatalf("No expected template found for Gupshup ]d: %s", gupshupTemplate.Id)
		}

		// Verificar os campos básicos
		assert.Equal(t, expectedTemplate.Name, processedTemplate.Name, "Template name mismatch for %s", gupshupTemplate.Id)
		assert.Equal(t, expectedTemplate.Language, processedTemplate.Language, "Template language mismatch for %s", gupshupTemplate.Id)
		assert.Equal(t, models.WhatsappBusinessTemplateStatusEnum(expectedTemplate.Status), processedTemplate.Status, "Template status mismatch for %s", gupshupTemplate.Id)
		assert.Equal(t, expectedTemplate.Category, processedTemplate.Category, "Template category mismatch for %s", gupshupTemplate.Id)
		assert.Equal(t, models.WhatsappBusinessMessageTypeEnum(expectedTemplate.MessageType), processedTemplate.MessageType, "Template messageType mismatch for %s", gupshupTemplate.Id)

		// Verificar os componentes
		// Primeiro, converter os componentes esperados de string JSON para objetos
		var expectedComponents []*models.WhatsappBusinessComponent
		if err := json.Unmarshal([]byte(expectedTemplate.Components), &expectedComponents); err != nil {
			t.Fatalf("Failed to unmarshal expected components: %v", err)
		}

		// Verificar se o número de componentes é o mesmo
		assert.Equal(t, len(expectedComponents), len(processedTemplate.Components),
			"Component count mismatch for %s. Expected: %d, Got: %d",
			gupshupTemplate.Id, len(expectedComponents), len(processedTemplate.Components))

		// Criar mapas de componentes por tipo para facilitar a comparação
		expectedComponentMap := make(map[string]*models.WhatsappBusinessComponent)
		for _, comp := range expectedComponents {
			expectedComponentMap[comp.Type] = comp
		}

		processedComponentMap := make(map[string]*models.WhatsappBusinessComponent)
		for _, comp := range processedTemplate.Components {
			processedComponentMap[comp.Type] = comp
		}

		// Verificar cada tipo de componente
		for _, compType := range []string{adapter_types.ComponentTypeHeader, adapter_types.ComponentTypeBody, adapter_types.ComponentTypeFooter, adapter_types.ComponentTypeButtons} {
			expectedComp, expectedExists := expectedComponentMap[compType]
			processedComp, processedExists := processedComponentMap[compType]

			// Verificar se ambos existem ou não existem
			assert.Equal(t, expectedExists, processedExists,
				"Component existence mismatch for type %s in template %s",
				compType, gupshupTemplate.Id)

			// Se ambos existem, verificar os detalhes
			if expectedExists && processedExists {
				assert.Equal(t, expectedComp.Text, processedComp.Text,
					"Component text mismatch for type %s in template %s",
					compType, gupshupTemplate.Id)

				assert.Equal(t, expectedComp.Format, processedComp.Format,
					"Component format mismatch for type %s in template %s",
					compType, gupshupTemplate.Id)

				// Verificar botões se for um componente de botões
				if compType == adapter_types.ComponentTypeButtons {
					assert.Equal(t, len(expectedComp.Buttons), len(processedComp.Buttons),
						"Button count mismatch for template %s", gupshupTemplate.Id)

					// Verificar cada botão
					for i, expectedBtn := range expectedComp.Buttons {
						if i < len(processedComp.Buttons) {
							processedBtn := processedComp.Buttons[i]
							assert.Equal(t, expectedBtn.Type, processedBtn.Type,
								"Button type mismatch for button %d in template %s",
								i, gupshupTemplate.Id)
							assert.Equal(t, expectedBtn.Text, processedBtn.Text,
								"Button text mismatch for button %d in template %s",
								i, gupshupTemplate.Id)
							assert.Equal(t, expectedBtn.PhoneNumber, processedBtn.PhoneNumber,
								"Button phone number mismatch for button %d in template %s",
								i, gupshupTemplate.Id)
							assert.Equal(t, expectedBtn.URL, processedBtn.URL,
								"Button URL mismatch for button %d in template %s",
								i, gupshupTemplate.Id)
						}
					}
				}
			}
		}
	}
}

func TestGetTemplatePayload(t *testing.T) {
	// return

	// adapter := &GupshupAdapter{
	// 	BaseWabaAdapter:    &base_waba_adapter.BaseWabaAdapter{},
	// 	templateRepository: repositories.WhatsappBusinessTemplateRepository(nil),
	// 	fileRepository:     repositories.FileRepository(nil),
	// 	storageService:     &storage.StorageService{},
	// }

	// // Criar um template de teste com imagem e botões
	// template := &models.WhatsappBusinessTemplate{
	// 	Name:     "feliz_natal_1",
	// 	Language: "pt_BR",
	// 	Category: "MARKETING",
	// 	Components: []*models.WhatsappBusinessComponent{
	// 		{
	// 			Type:   adapter_types.ComponentTypeHeader,
	// 			Format: "IMAGE",
	// 			Example: &models.WhatsappBusinessComponentExample{
	// 				HeaderHandle: []string{
	// 					"https://example.com/image.jpg",
	// 				},
	// 			},
	// 		},
	// 		{
	// 			Type:   adapter_types.ComponentTypeBody,
	// 			Text:   "Ola {{1}}\nFeliz natal, boas festas!",
	// 			Params: []string{"Contato"},
	// 		},
	// 		{
	// 			Type: adapter_types.ComponentTypeFooter,
	// 			Text: "boas festas",
	// 		},
	// 		{
	// 			Type: adapter_types.ComponentTypeButtons,
	// 			Buttons: []*models.WhatsappBusinessComponentParameterButton{
	// 				{
	// 					Type: "QUICK_REPLY",
	// 					Text: "feliz natal",
	// 				},
	// 			},
	// 		},
	// 	},
	// }

	// // Obter o payload
	// formData, err := adapter.getTemplatePayload(template)
	// assert.NoError(t, err)

	// // Validar campos básicos
	// assert.Equal(t, "feliz_natal_1", formData.Get("elementName"))
	// assert.Equal(t, "pt_BR", formData.Get("languageCode"))
	// assert.Equal(t, "MARKETING", formData.Get("category"))
	// assert.Equal(t, "true", formData.Get("allow_category_change"))
	// assert.Equal(t, "false", formData.Get("addSecurityRecommendation"))

	// // Validar campos de mídia
	// assert.Equal(t, "IMAGE", formData.Get("templateType"))
	// assert.Equal(t, "IMAGE", formData.Get("vertical"))

	// // ExampleMedia é um id gerado no upload da mídia para gupshup
	// // assert.Equal(t, "https://example.com/image.jpg", formData.Get("exampleMedia"))

	// // Validar corpo
	// assert.Equal(t, "Ola {{1}}\nFeliz natal, boas festas!", formData.Get("content"))
	// assert.Equal(t, "Ola Contato\nFeliz natal, boas festas!", formData.Get("example"))

	// // Validar rodapé
	// assert.Equal(t, "boas festas", formData.Get("footer"))

	// // Validar botões
	// buttons := formData.Get("buttons")
	// assert.Contains(t, buttons, `"type":"QUICK_REPLY"`)
	// assert.Contains(t, buttons, `"text":"feliz natal"`)
}
